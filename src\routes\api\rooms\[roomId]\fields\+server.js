import { json } from '@sveltejs/kit';

// GET endpoint to fetch room fields
export const GET = async ({ params, locals: { supabase } }) => {
  try {
    const { roomId } = params;

    if (!roomId) {
      return json({ success: false, message: 'Room ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('rooms')
      .select('enabled_fields, field_names')
      .eq('id', roomId)
      .single();

    if (error) {
      console.error('Failed to fetch room fields:', error);
      return json({ success: false, message: error.message }, { status: 500 });
    }

    return json({
      success: true,
      data: {
        ...data?.enabled_fields || {},
        field_names: data?.field_names || {}
      }
    });
  } catch (error) {
    console.error('Error in get room fields API:', error);
    return json({ success: false, message: 'Server error' }, { status: 500 });
  }
};

// POST endpoint to update room fields
export const POST = async ({ params, request, locals: { supabase } }) => {
  try {
    const { roomId } = params;
    const requestData = await request.json();
    const { fields: newFields, field_names: newFieldNames } = requestData;

    // Validate the input
    if (!roomId || (!newFields && !newFieldNames)) {
      return json({ success: false, message: 'Missing required data' }, { status: 400 });
    }

    // Fetch the current room data first
    const { data: currentRoom, error: fetchError } = await supabase
      .from('rooms')
      .select('enabled_fields, field_names')
      .eq('id', roomId)
      .single();

    if (fetchError) {
      console.error('Failed to fetch current room:', fetchError);
      return json({ success: false, message: fetchError.message }, { status: 500 });
    }

    // Prepare update object
    const updateData = {};

    // Merge the existing fields with the new fields to ensure all properties are preserved
    if (newFields) {
      const currentFields = currentRoom?.enabled_fields || {};
      updateData.enabled_fields = {
        ...currentFields,
        ...newFields
      };
    }

    // Merge the existing field names with the new field names
    if (newFieldNames) {
      const currentFieldNames = currentRoom?.field_names || {};
      updateData.field_names = {
        ...currentFieldNames,
        ...newFieldNames
      };
    }

    // Update the room with the complete data object
    const { data, error } = await supabase
      .from('rooms')
      .update(updateData)
      .eq('id', roomId)
      .select();

    if (error) {
      console.error('Failed to update fields:', error);
      return json({ success: false, message: error.message }, { status: 500 });
    }

    return json({ success: true, data });
  } catch (error) {
    console.error('Error in update room fields API:', error);
    return json({ success: false, message: 'Server error' }, { status: 500 });
  }
};