{"name": "quiz.karczma.moe", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview --host", "format": "prettier --write .", "lint": "prettier --check ."}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/kit": "^2.0.0", "@sveltejs/vite-plugin-svelte": "^4.0.0", "autoprefixer": "^10.4.20", "bits-ui": "^1.8.0", "clsx": "^2.1.1", "cmdk-sv": "^0.0.18", "lucide-svelte": "^0.468.0", "mode-watcher": "^0.5.0", "prettier": "^3.3.2", "prettier-plugin-svelte": "^3.2.6", "prettier-plugin-tailwindcss": "^0.6.5", "svelte": "^5.38.1", "svelte-sonner": "^0.3.28", "tailwind-merge": "^2.6.0", "tailwind-variants": "^0.3.1", "tailwindcss": "^3.4.9", "vite": "^5.0.3", "vite-plugin-mkcert": "^1.17.8"}, "dependencies": {"@algolia/autocomplete-core": "^1.17.8", "@algolia/autocomplete-js": "^1.17.8", "@algolia/autocomplete-theme-classic": "^1.17.8", "@meilisearch/autocomplete-client": "^0.5.0", "@meilisearch/instant-meilisearch": "^0.23.0", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.46.1", "@sveltejs/adapter-node": "^5.2.12", "@tailwindcss/typography": "^0.5.15", "awesomplete": "^1.1.7", "better-sqlite3": "^11.10.0", "instantsearch.js": "^4.75.6", "lodash": "^4.17.21", "typesense": "^1.8.2", "uuid": "^11.1.0"}}